import React, { useState } from 'react';
import AnnotationSelector from './AnnotationSelector';
import { loiEvaluationsApi } from '../services/api';

// Modal component for displaying input text
const InputTextModal = ({ isOpen, onClose, inputText }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>Input Text</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontSize: '14px',
          lineHeight: '1.6',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '500px',
          overflowY: 'auto',
          padding: '10px',
          backgroundColor: '#f8f9fa',
          borderRadius: '4px'
        }}>
          {inputText}
        </div>
      </div>
    </div>
  );
};

// Evidence Modal Component
const EvidenceModal = ({ isOpen, onClose, competency, score, evidences }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative',
          minWidth: '500px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0, color: '#333' }}>
            {competency} - Level {score} Evidence
          </h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>

        {evidences && evidences.length > 0 ? (
          <div>
            <p style={{ marginBottom: '12px', color: '#666', fontSize: '14px' }}>
              Evidence found for achieving Level {score}:
            </p>
            <ul style={{
              padding: '0',
              margin: '0',
              listStyle: 'none'
            }}>
              {evidences.map((evidence, index) => (
                <li key={index} style={{
                  padding: '12px',
                  margin: '8px 0',
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #e9ecef',
                  borderRadius: '4px',
                  fontSize: '14px',
                  lineHeight: '1.5',
                  display: 'flex',
                  alignItems: 'flex-start'
                }}>
                  <span style={{
                    display: 'inline-block',
                    width: '24px',
                    height: '24px',
                    backgroundColor: '#28a745',
                    color: 'white',
                    borderRadius: '50%',
                    textAlign: 'center',
                    lineHeight: '24px',
                    fontSize: '12px',
                    marginRight: '12px',
                    fontWeight: 'bold',
                    flexShrink: 0
                  }}>
                    {index + 1}
                  </span>
                  <span style={{ flex: 1 }}>
                    {evidence}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            color: '#666',
            fontStyle: 'italic',
            padding: '40px 20px'
          }}>
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>
              No evidence data available for this score.
            </div>
            <small style={{ fontSize: '12px', color: '#999' }}>
              This may be from an older evaluation format that didn't include evidence details.
            </small>
          </div>
        )}
      </div>
    </div>
  );
};

// Modal component for displaying prompt content
const PromptModal = ({ isOpen, onClose, promptData, promptName, loading }) => {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '20px',
        maxWidth: '80%',
        maxHeight: '80%',
        overflow: 'auto',
        position: 'relative'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '15px',
          borderBottom: '1px solid #e9ecef',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0, color: '#495057' }}>{promptName}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#6c757d'
            }}
          >
            ×
          </button>
        </div>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            Loading prompt...
          </div>
        ) : (
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '15px',
            borderRadius: '4px',
            border: '1px solid #e9ecef',
            maxHeight: '600px',
            overflow: 'auto'
          }}>
            <pre style={{
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.5'
            }}>
              {promptData}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

const LoIResultsTable = ({ evaluations, onAnnotationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [inputModalState, setInputModalState] = useState({
    isOpen: false,
    inputText: ''
  });
  const [evidenceModalState, setEvidenceModalState] = useState({
    isOpen: false,
    competency: '',
    score: 0,
    evidences: []
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const openInputModal = (inputText) => {
    setInputModalState({
      isOpen: true,
      inputText
    });
  };

  const closeInputModal = () => {
    setInputModalState({
      isOpen: false,
      inputText: ''
    });
  };

  const openEvidenceModal = (competency, score, geminiResponse) => {
    let evidences = [];

    if (geminiResponse && geminiResponse.results) {
      const competencyData = geminiResponse.results.find(comp =>
        comp.competency === competency
      );

      if (competencyData && competencyData.levels) {
        // Get evidences from the highest level that has check=true and matches the score
        const levelWithScore = competencyData.levels.find(level =>
          level.order === score && level.check === true
        );

        if (levelWithScore && levelWithScore.evidences) {
          evidences = levelWithScore.evidences;
        }
      }
    }

    setEvidenceModalState({
      isOpen: true,
      competency,
      score,
      evidences
    });
  };

  const closeEvidenceModal = () => {
    setEvidenceModalState({
      isOpen: false,
      competency: '',
      score: 0,
      evidences: []
    });
  };

  // Prompt modal state and functions
  const [promptModalState, setPromptModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });

  const showPrompt = (promptContent, promptVersion) => {
    setPromptModalState({
      isOpen: true,
      promptData: promptContent,
      promptName: `LoI Scoring Prompt v${promptVersion}`,
      loading: false
    });
  };

  const closePromptModal = () => {
    setPromptModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const getStatusDisplay = (status) => {
    switch (status) {
      case 'in_progress':
        return { text: 'Processing...', color: '#ffc107', bgColor: '#fff3cd' };
      case 'completed':
        return { text: 'Completed', color: '#28a745', bgColor: '#d4edda' };
      case 'error':
        return { text: 'Error', color: '#dc3545', bgColor: '#f8d7da' };
      default:
        return { text: status, color: '#6c757d', bgColor: '#e9ecef' };
    }
  };

  const formatAccuracy = (accuracy) => {
    if (accuracy === null || accuracy === undefined) return 'N/A';
    return `${(accuracy * 100).toFixed(1)}%`;
  };

  const getDiffColor = (diff) => {
    if (diff === 0) return '#28a745'; // Green for correct
    return '#dc3545'; // Red for incorrect
  };

  const getDiffBackgroundColor = (diff) => {
    if (diff === 0) return '#d4edda'; // Light green for correct
    return '#f8d7da'; // Light red for incorrect
  };

  const formatDiff = (diff) => {
    if (diff === 0) return '0';
    return diff > 0 ? `+${diff}` : `${diff}`;
  };

  if (evaluations.length === 0) {
    return (
      <div style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '20px',
        backgroundColor: 'white',
        textAlign: 'center'
      }}>
        <h3>LoI Scoring Evaluation Results</h3>
        <p style={{ color: '#666' }}>No evaluations run yet. Use the form above to run your first evaluation.</p>
      </div>
    );
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: 'white',
      overflow: 'hidden'
    }}>
      <h3 style={{
        margin: 0,
        padding: '16px',
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #ddd'
      }}>
        LoI Scoring Evaluation Results ({evaluations.length})
      </h3>

      <div style={{ overflowX: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Dataset
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Prompt Version
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Status
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Continuous Learning Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Driving for Result Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Rows Processed
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Annotation
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {evaluation.dataset_name || 'Unknown Dataset'}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.prompt_version && evaluation.prompt_content ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          showPrompt(evaluation.prompt_content, evaluation.prompt_version);
                        }}
                        style={{
                          background: 'none',
                          border: '1px solid #007bff',
                          borderRadius: '4px',
                          padding: '4px 8px',
                          cursor: 'pointer',
                          fontSize: '12px',
                          color: '#007bff'
                        }}
                      >
                        📝 v{evaluation.prompt_version}
                      </button>
                    ) : (
                      <span style={{ color: '#6c757d', fontStyle: 'italic' }}>N/A</span>
                    )}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: getStatusDisplay(evaluation.status).color,
                      backgroundColor: getStatusDisplay(evaluation.status).bgColor
                    }}>
                      {getStatusDisplay(evaluation.status).text}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '16px' }}>
                      {formatAccuracy(evaluation.continuous_learning_accuracy)}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '16px' }}>
                      {formatAccuracy(evaluation.driving_for_result_accuracy)}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.total_rows_processed || 0}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={onAnnotationUpdate}
                      apiService={loiEvaluationsApi}
                    />
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: expandedRow === evaluation.id ? '#007bff' : 'white',
                        color: expandedRow === evaluation.id ? 'white' : '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Hide Details' : 'View Details'}
                    </button>
                  </td>
                </tr>

                {/* Expanded row content */}
                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="9" style={{ padding: '0', backgroundColor: '#f8f9fa' }}>
                      <div style={{ padding: '20px' }}>
                        {evaluation.status === 'completed' && evaluation.output && evaluation.output.results ? (
                          <div>
                            <h4 style={{ margin: '0 0 16px 0' }}>Detailed Results:</h4>
                            <div style={{ overflowX: 'auto' }}>
                              <table style={{ width: '100%', borderCollapse: 'collapse', backgroundColor: 'white' }}>
                                <thead>
                                  <tr style={{ backgroundColor: '#e9ecef' }}>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Row</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Input Text</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Expected CL</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Actual CL</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>CL Diff</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Expected DR</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Actual DR</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>DR Diff</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {evaluation.output.results.map((result, index) => (
                                    <tr key={index}>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.rowIndex}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>
                                        <button
                                          onClick={() => openInputModal(result.inputText)}
                                          style={{
                                            background: 'none',
                                            border: '1px solid #007bff',
                                            borderRadius: '4px',
                                            color: '#007bff',
                                            cursor: 'pointer',
                                            padding: '4px 8px',
                                            fontSize: '11px'
                                          }}
                                        >
                                          View Text
                                        </button>
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.expectedContinuousLearning}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        <button
                                          onClick={() => openEvidenceModal('Continuous Learning', result.actualContinuousLearning, result.geminiResponse)}
                                          style={{
                                            background: 'none',
                                            border: '1px solid #28a745',
                                            borderRadius: '4px',
                                            color: '#28a745',
                                            cursor: 'pointer',
                                            padding: '2px 6px',
                                            fontSize: '11px',
                                            fontWeight: 'bold'
                                          }}
                                        >
                                          {result.actualContinuousLearning}
                                        </button>
                                      </td>
                                      <td style={{
                                        padding: '8px',
                                        border: '1px solid #ddd',
                                        textAlign: 'center',
                                        fontSize: '12px',
                                        fontWeight: 'bold',
                                        color: getDiffColor(result.continuousLearningDiff),
                                        backgroundColor: getDiffBackgroundColor(result.continuousLearningDiff)
                                      }}>
                                        {formatDiff(result.continuousLearningDiff)}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.expectedDrivingForResult}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        <button
                                          onClick={() => openEvidenceModal('Driving for result', result.actualDrivingForResult, result.geminiResponse)}
                                          style={{
                                            background: 'none',
                                            border: '1px solid #28a745',
                                            borderRadius: '4px',
                                            color: '#28a745',
                                            cursor: 'pointer',
                                            padding: '2px 6px',
                                            fontSize: '11px',
                                            fontWeight: 'bold'
                                          }}
                                        >
                                          {result.actualDrivingForResult}
                                        </button>
                                      </td>
                                      <td style={{
                                        padding: '8px',
                                        border: '1px solid #ddd',
                                        textAlign: 'center',
                                        fontSize: '12px',
                                        fontWeight: 'bold',
                                        color: getDiffColor(result.drivingForResultDiff),
                                        backgroundColor: getDiffBackgroundColor(result.drivingForResultDiff)
                                      }}>
                                        {formatDiff(result.drivingForResultDiff)}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        ) : (
                          <div style={{ textAlign: 'center', color: '#666', fontStyle: 'italic' }}>
                            {evaluation.status === 'error' ? 'Evaluation failed' : 'Processing...'}
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Input Text Modal */}
      <InputTextModal
        isOpen={inputModalState.isOpen}
        onClose={closeInputModal}
        inputText={inputModalState.inputText}
      />

      {/* Evidence Modal */}
      <EvidenceModal
        isOpen={evidenceModalState.isOpen}
        onClose={closeEvidenceModal}
        competency={evidenceModalState.competency}
        score={evidenceModalState.score}
        evidences={evidenceModalState.evidences}
      />

      {/* Prompt Modal */}
      <PromptModal
        isOpen={promptModalState.isOpen}
        onClose={closePromptModal}
        promptData={promptModalState.promptData}
        promptName={promptModalState.promptName}
        loading={promptModalState.loading}
      />
    </div>
  );
};

export default LoIResultsTable;
